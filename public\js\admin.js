// ===== Global Variables =====
let sessionId = null;
let contentData = {};
let currentSection = 'personal';

// ===== Initialize Admin Panel =====
document.addEventListener('DOMContentLoaded', function() {
    initializeAdmin();
});

function initializeAdmin() {
    // Check if already logged in
    sessionId = localStorage.getItem('adminSessionId');
    if (sessionId) {
        showAdminPanel();
        loadContent();
    }
    
    // Setup event listeners
    setupEventListeners();
}

function setupEventListeners() {
    // Login form
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // Content forms
    const forms = ['personal-form', 'about-form', 'contact-form'];
    forms.forEach(formId => {
        const form = document.getElementById(formId);
        if (form) {
            form.addEventListener('submit', handleFormSubmit);
        }
    });
}

// ===== Authentication =====
async function handleLogin(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const credentials = {
        username: formData.get('username'),
        password: formData.get('password')
    };
    
    try {
        const response = await fetch('/api/admin/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(credentials)
        });
        
        const result = await response.json();
        
        if (result.success) {
            sessionId = result.sessionId;
            localStorage.setItem('adminSessionId', sessionId);
            showAdminPanel();
            loadContent();
            showNotification('تم تسجيل الدخول بنجاح', 'success');
        } else {
            showNotification(result.message || 'فشل في تسجيل الدخول', 'error');
        }
    } catch (error) {
        console.error('Login error:', error);
        showNotification('حدث خطأ في تسجيل الدخول', 'error');
    }
}

function logout() {
    sessionId = null;
    localStorage.removeItem('adminSessionId');
    document.getElementById('login-section').style.display = 'block';
    document.getElementById('admin-panel').style.display = 'none';
    showNotification('تم تسجيل الخروج بنجاح', 'success');
}

function showAdminPanel() {
    document.getElementById('login-section').style.display = 'none';
    document.getElementById('admin-panel').style.display = 'block';
}

// ===== Content Management =====
async function loadContent() {
    try {
        const response = await fetch('/api/content');
        if (!response.ok) {
            throw new Error('Failed to load content');
        }
        
        contentData = await response.json();
        populateAdminForms();
        loadDynamicSections();
    } catch (error) {
        console.error('Error loading content:', error);
        showNotification('فشل في تحميل المحتوى', 'error');
    }
}

function populateAdminForms() {
    // Personal info form
    if (contentData.personalInfo) {
        const personalInfo = contentData.personalInfo;
        document.getElementById('fullName').value = personalInfo.fullName || '';
        document.getElementById('displayName').value = personalInfo.displayName || '';
        document.getElementById('title').value = personalInfo.title || '';
        document.getElementById('description').value = personalInfo.description || '';
        document.getElementById('birthDate').value = personalInfo.birthDate || '';
        document.getElementById('nationality').value = personalInfo.nationality || '';
        document.getElementById('education').value = personalInfo.education || '';

        // Update profile image preview
        const currentProfileImage = document.getElementById('current-profile-image');
        if (currentProfileImage && personalInfo.profileImage) {
            currentProfileImage.src = personalInfo.profileImage;
        }
    }
    
    // About form
    if (contentData.about) {
        document.getElementById('bio').value = contentData.about.bio || '';
        document.getElementById('skills').value = contentData.about.skills ? contentData.about.skills.join(', ') : '';
        document.getElementById('interests').value = contentData.about.interests ? contentData.about.interests.join(', ') : '';
    }
    
    // Contact form
    if (contentData.contact) {
        document.getElementById('email').value = contentData.contact.email || '';
        document.getElementById('phone').value = contentData.contact.phone || '';
        document.getElementById('address').value = contentData.contact.address || '';
        
        if (contentData.contact.social) {
            document.getElementById('facebook').value = contentData.contact.social.facebook || '';
            document.getElementById('instagram').value = contentData.contact.social.instagram || '';
            document.getElementById('linkedin').value = contentData.contact.social.linkedin || '';
            document.getElementById('github').value = contentData.contact.social.github || '';
        }
    }
}

async function handleFormSubmit(e) {
    e.preventDefault();
    
    const formId = e.target.id;
    const formData = new FormData(e.target);
    
    try {
        let updatedData = { ...contentData };
        
        switch (formId) {
            case 'personal-form':
                updatedData.personalInfo = {
                    ...updatedData.personalInfo,
                    fullName: formData.get('fullName'),
                    displayName: formData.get('displayName'),
                    title: formData.get('title'),
                    description: formData.get('description'),
                    birthDate: formData.get('birthDate'),
                    nationality: formData.get('nationality'),
                    education: formData.get('education')
                };
                break;
                
            case 'about-form':
                updatedData.about = {
                    ...updatedData.about,
                    bio: formData.get('bio'),
                    skills: formData.get('skills').split(',').map(s => s.trim()).filter(s => s),
                    interests: formData.get('interests').split(',').map(s => s.trim()).filter(s => s)
                };
                break;
                
            case 'contact-form':
                updatedData.contact = {
                    ...updatedData.contact,
                    email: formData.get('email'),
                    phone: formData.get('phone'),
                    address: formData.get('address'),
                    social: {
                        facebook: formData.get('facebook'),
                        instagram: formData.get('instagram'),
                        linkedin: formData.get('linkedin'),
                        github: formData.get('github')
                    }
                };
                break;
        }
        
        await saveContent(updatedData);
        showNotification('تم حفظ التغييرات بنجاح', 'success');
        
    } catch (error) {
        console.error('Error saving content:', error);
        showNotification('فشل في حفظ التغييرات', 'error');
    }
}

async function saveContent(data) {
    try {
        const response = await fetch('/api/admin/content', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${sessionId}`
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || 'Failed to save content');
        }

        const result = await response.json();
        contentData = data;
        return result;
    } catch (error) {
        console.error('Save content error:', error);
        throw error;
    }
}

// ===== Section Navigation =====
function showSection(sectionName, buttonElement) {
    // Hide all sections
    document.querySelectorAll('.admin-section').forEach(section => {
        section.classList.remove('active');
    });

    // Show target section
    const targetSection = document.getElementById(`${sectionName}-section`);
    if (targetSection) {
        targetSection.classList.add('active');
    }

    // Update navigation
    document.querySelectorAll('.admin-nav button').forEach(btn => {
        btn.classList.remove('active');
    });

    // If buttonElement is provided, make it active
    if (buttonElement) {
        buttonElement.classList.add('active');
    } else {
        // Find the button by onclick attribute
        const buttons = document.querySelectorAll('.admin-nav button');
        buttons.forEach(btn => {
            if (btn.getAttribute('onclick') && btn.getAttribute('onclick').includes(sectionName)) {
                btn.classList.add('active');
            }
        });
    }

    currentSection = sectionName;

    // Load section-specific content
    if (sectionName === 'projects') {
        loadProjects();
    } else if (sectionName === 'services') {
        loadServices();
    } else if (sectionName === 'products') {
        loadProducts();
    } else if (sectionName === 'testimonials') {
        loadTestimonials();
    }
}

// ===== Dynamic Sections =====
function loadDynamicSections() {
    loadProjects();
    loadServices();
    loadProducts();
    loadTestimonials();
}

function loadProjects() {
    const projectsList = document.getElementById('projects-list');
    if (!projectsList) {
        console.error('Projects list element not found');
        return;
    }

    if (!contentData.projects || contentData.projects.length === 0) {
        projectsList.innerHTML = '<p style="text-align: center; color: #666;">لا توجد مشاريع حالياً. اضغط "إضافة مشروع جديد" لإضافة مشروع.</p>';
        return;
    }

    projectsList.innerHTML = contentData.projects.map((project, index) => `
        <div class="card">
            <h3>${project.title || 'بدون عنوان'}</h3>
            <p>${project.description || 'بدون وصف'}</p>
            <p><strong>التقنيات:</strong> ${project.technologies ? project.technologies.join(', ') : 'غير محدد'}</p>
            <div style="margin-top: 1rem;">
                <button onclick="editProject(${index})" class="btn btn-primary">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button onclick="deleteProject(${index})" class="btn btn-danger">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </div>
    `).join('');
}

function loadServices() {
    const servicesList = document.getElementById('services-list');
    if (!servicesList) {
        console.error('Services list element not found');
        return;
    }

    if (!contentData.services || contentData.services.length === 0) {
        servicesList.innerHTML = '<p style="text-align: center; color: #666;">لا توجد خدمات حالياً. اضغط "إضافة خدمة جديدة" لإضافة خدمة.</p>';
        return;
    }

    servicesList.innerHTML = contentData.services.map((service, index) => `
        <div class="card">
            <h3>${service.title || 'بدون عنوان'}</h3>
            <p>${service.description || 'بدون وصف'}</p>
            <p><strong>السعر:</strong> ${service.price || 'غير محدد'}</p>
            <div style="margin-top: 1rem;">
                <button onclick="editService(${index})" class="btn btn-primary">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button onclick="deleteService(${index})" class="btn btn-danger">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </div>
    `).join('');
}

function loadProducts() {
    const productsList = document.getElementById('products-list');
    if (!productsList) {
        console.error('Products list element not found');
        return;
    }

    if (!contentData.products || contentData.products.length === 0) {
        productsList.innerHTML = '<p style="text-align: center; color: #666;">لا توجد منتجات حالياً. اضغط "إضافة منتج جديد" لإضافة منتج.</p>';
        return;
    }

    productsList.innerHTML = contentData.products.map((product, index) => `
        <div class="card">
            <h3>${product.title || 'بدون عنوان'}</h3>
            <p>${product.description || 'بدون وصف'}</p>
            <p><strong>السعر:</strong> ${product.price || 'غير محدد'}</p>
            <div style="margin-top: 1rem;">
                <button onclick="editProduct(${index})" class="btn btn-primary">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button onclick="deleteProduct(${index})" class="btn btn-danger">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </div>
    `).join('');
}

function loadTestimonials() {
    const testimonialsList = document.getElementById('testimonials-list');
    if (!testimonialsList) {
        console.error('Testimonials list element not found');
        return;
    }

    if (!contentData.testimonials || contentData.testimonials.length === 0) {
        testimonialsList.innerHTML = '<p style="text-align: center; color: #666;">لا توجد توصيات حالياً. اضغط "إضافة توصية جديدة" لإضافة توصية.</p>';
        return;
    }

    testimonialsList.innerHTML = contentData.testimonials.map((testimonial, index) => `
        <div class="card">
            <h3>${testimonial.name || 'بدون اسم'}</h3>
            <p><strong>المنصب:</strong> ${testimonial.position || 'غير محدد'}</p>
            <p>${testimonial.text || 'بدون نص'}</p>
            <p><strong>التقييم:</strong> ${testimonial.rating || 0}/5</p>
            <div style="margin-top: 1rem;">
                <button onclick="editTestimonial(${index})" class="btn btn-primary">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button onclick="deleteTestimonial(${index})" class="btn btn-danger">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </div>
    `).join('');
}

// ===== CRUD Operations =====
async function addProject() {
    const title = prompt('عنوان المشروع:');
    if (!title) return;

    const description = prompt('وصف المشروع:');
    if (!description) return;

    const technologies = prompt('التقنيات المستخدمة (منفصلة بفاصلة):');
    if (!technologies) return;

    const link = prompt('رابط المشروع:') || '#';
    const github = prompt('رابط GitHub (اختياري):') || '';

    try {
        const newProject = {
            id: Date.now(),
            title: title.trim(),
            description: description.trim(),
            technologies: technologies.split(',').map(t => t.trim()).filter(t => t),
            link: link.trim(),
            github: github.trim(),
            image: '/images/project-placeholder.jpg'
        };

        contentData.projects = contentData.projects || [];
        contentData.projects.push(newProject);

        await saveContent(contentData);
        loadProjects();
        showNotification('تم إضافة المشروع بنجاح', 'success');
    } catch (error) {
        console.error('Error adding project:', error);
        showNotification('فشل في إضافة المشروع', 'error');
    }
}

function editProject(index) {
    const project = contentData.projects[index];
    if (!project) return;

    const title = prompt('عنوان المشروع:', project.title);
    const description = prompt('وصف المشروع:', project.description);
    const technologies = prompt('التقنيات المستخدمة (منفصلة بفاصلة):', project.technologies ? project.technologies.join(', ') : '');
    const link = prompt('رابط المشروع:', project.link);
    const github = prompt('رابط GitHub (اختياري):', project.github);

    if (title !== null && description !== null && technologies !== null) {
        contentData.projects[index] = {
            ...project,
            title: title || project.title,
            description: description || project.description,
            technologies: technologies ? technologies.split(',').map(t => t.trim()) : project.technologies,
            link: link || project.link,
            github: github || project.github
        };

        saveContent(contentData);
        loadProjects();
        showNotification('تم تحديث المشروع بنجاح', 'success');
    }
}

function deleteProject(index) {
    if (confirm('هل أنت متأكد من حذف هذا المشروع؟')) {
        contentData.projects.splice(index, 1);
        saveContent(contentData);
        loadProjects();
        showNotification('تم حذف المشروع بنجاح', 'success');
    }
}

async function addService() {
    const title = prompt('عنوان الخدمة:');
    if (!title) return;

    const description = prompt('وصف الخدمة:');
    if (!description) return;

    const price = prompt('السعر:');
    if (!price) return;

    const icon = prompt('أيقونة الخدمة (Font Awesome class):') || 'fas fa-cog';

    try {
        const newService = {
            id: Date.now(),
            title: title.trim(),
            description: description.trim(),
            price: price.trim(),
            icon: icon.trim()
        };

        contentData.services = contentData.services || [];
        contentData.services.push(newService);

        await saveContent(contentData);
        loadServices();
        showNotification('تم إضافة الخدمة بنجاح', 'success');
    } catch (error) {
        console.error('Error adding service:', error);
        showNotification('فشل في إضافة الخدمة', 'error');
    }
}

function editService(index) {
    const service = contentData.services[index];
    if (!service) return;

    const title = prompt('عنوان الخدمة:', service.title);
    const description = prompt('وصف الخدمة:', service.description);
    const price = prompt('السعر:', service.price);
    const icon = prompt('أيقونة الخدمة (Font Awesome class):', service.icon);

    if (title !== null && description !== null && price !== null) {
        contentData.services[index] = {
            ...service,
            title: title || service.title,
            description: description || service.description,
            price: price || service.price,
            icon: icon || service.icon
        };

        saveContent(contentData);
        loadServices();
        showNotification('تم تحديث الخدمة بنجاح', 'success');
    }
}

function deleteService(index) {
    if (confirm('هل أنت متأكد من حذف هذه الخدمة؟')) {
        contentData.services.splice(index, 1);
        saveContent(contentData);
        loadServices();
        showNotification('تم حذف الخدمة بنجاح', 'success');
    }
}

async function addProduct() {
    const title = prompt('عنوان المنتج:');
    if (!title) return;

    const description = prompt('وصف المنتج:');
    if (!description) return;

    const price = prompt('السعر:');
    if (!price) return;

    const downloadLink = prompt('رابط التحميل:') || '#';

    try {
        const newProduct = {
            id: Date.now(),
            title: title.trim(),
            description: description.trim(),
            price: price.trim(),
            downloadLink: downloadLink.trim(),
            image: '/images/product-placeholder.jpg'
        };

        contentData.products = contentData.products || [];
        contentData.products.push(newProduct);

        await saveContent(contentData);
        loadProducts();
        showNotification('تم إضافة المنتج بنجاح', 'success');
    } catch (error) {
        console.error('Error adding product:', error);
        showNotification('فشل في إضافة المنتج', 'error');
    }
}

function editProduct(index) {
    const product = contentData.products[index];
    if (!product) return;

    const title = prompt('عنوان المنتج:', product.title);
    const description = prompt('وصف المنتج:', product.description);
    const price = prompt('السعر:', product.price);
    const downloadLink = prompt('رابط التحميل:', product.downloadLink);

    if (title !== null && description !== null && price !== null) {
        contentData.products[index] = {
            ...product,
            title: title || product.title,
            description: description || product.description,
            price: price || product.price,
            downloadLink: downloadLink || product.downloadLink
        };

        saveContent(contentData);
        loadProducts();
        showNotification('تم تحديث المنتج بنجاح', 'success');
    }
}

function deleteProduct(index) {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
        contentData.products.splice(index, 1);
        saveContent(contentData);
        loadProducts();
        showNotification('تم حذف المنتج بنجاح', 'success');
    }
}

async function addTestimonial() {
    const name = prompt('اسم العميل:');
    if (!name) return;

    const position = prompt('المنصب:');
    if (!position) return;

    const text = prompt('نص التوصية:');
    if (!text) return;

    const rating = prompt('التقييم (1-5):');
    if (!rating || isNaN(rating) || rating < 1 || rating > 5) {
        showNotification('يرجى إدخال تقييم صحيح من 1 إلى 5', 'error');
        return;
    }

    try {
        const newTestimonial = {
            id: Date.now(),
            name: name.trim(),
            position: position.trim(),
            text: text.trim(),
            rating: parseInt(rating),
            image: '/images/testimonial-placeholder.jpg'
        };

        contentData.testimonials = contentData.testimonials || [];
        contentData.testimonials.push(newTestimonial);

        await saveContent(contentData);
        loadTestimonials();
        showNotification('تم إضافة التوصية بنجاح', 'success');
    } catch (error) {
        console.error('Error adding testimonial:', error);
        showNotification('فشل في إضافة التوصية', 'error');
    }
}

function editTestimonial(index) {
    const testimonial = contentData.testimonials[index];
    if (!testimonial) return;

    const name = prompt('اسم العميل:', testimonial.name);
    const position = prompt('المنصب:', testimonial.position);
    const text = prompt('نص التوصية:', testimonial.text);
    const rating = prompt('التقييم (1-5):', testimonial.rating);

    if (name !== null && position !== null && text !== null && rating !== null) {
        contentData.testimonials[index] = {
            ...testimonial,
            name: name || testimonial.name,
            position: position || testimonial.position,
            text: text || testimonial.text,
            rating: parseInt(rating) || testimonial.rating
        };

        saveContent(contentData);
        loadTestimonials();
        showNotification('تم تحديث التوصية بنجاح', 'success');
    }
}

function deleteTestimonial(index) {
    if (confirm('هل أنت متأكد من حذف هذه التوصية؟')) {
        contentData.testimonials.splice(index, 1);
        saveContent(contentData);
        loadTestimonials();
        showNotification('تم حذف التوصية بنجاح', 'success');
    }
}

// ===== Image Upload =====
async function uploadImage(input) {
    const file = input.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
        showNotification('يرجى اختيار ملف صورة صالح', 'error');
        return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
        showNotification('حجم الصورة كبير جداً. الحد الأقصى 5 ميجابايت', 'error');
        return;
    }

    const formData = new FormData();
    formData.append('image', file);

    try {
        showNotification('جاري رفع الصورة...', 'info');

        const response = await fetch('/api/admin/upload', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${sessionId}`
            },
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            showNotification('تم رفع الصورة بنجاح', 'success');
            displayUploadedImage(result.path, result.filename);
            input.value = ''; // Clear the input
        } else {
            showNotification('فشل في رفع الصورة', 'error');
        }
    } catch (error) {
        console.error('Upload error:', error);
        showNotification('حدث خطأ في رفع الصورة', 'error');
    }
}

function displayUploadedImage(imagePath, filename) {
    const uploadedImagesContainer = document.getElementById('uploaded-images');
    if (!uploadedImagesContainer) return;

    const imageCard = document.createElement('div');
    imageCard.className = 'card';
    imageCard.innerHTML = `
        <img src="${imagePath}" alt="${filename}" style="width: 100%; height: 200px; object-fit: cover; border-radius: 8px; margin-bottom: 1rem;">
        <h4>${filename}</h4>
        <p><strong>المسار:</strong> ${imagePath}</p>
        <button onclick="copyImagePath('${imagePath}')" class="btn btn-primary">
            <i class="fas fa-copy"></i> نسخ المسار
        </button>
    `;

    uploadedImagesContainer.insertBefore(imageCard, uploadedImagesContainer.firstChild);
}

function copyImagePath(path) {
    navigator.clipboard.writeText(path).then(() => {
        showNotification('تم نسخ مسار الصورة', 'success');
    }).catch(() => {
        showNotification('فشل في نسخ المسار', 'error');
    });
}

// ===== Profile Image Update =====
async function updateProfileImage(input) {
    const file = input.files[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
        showNotification('يرجى اختيار ملف صورة صالح', 'error');
        return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
        showNotification('حجم الصورة كبير جداً. الحد الأقصى 5 ميجابايت', 'error');
        return;
    }

    const formData = new FormData();
    formData.append('image', file);

    try {
        showNotification('جاري رفع الصورة الشخصية...', 'info');

        const response = await fetch('/api/admin/upload', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${sessionId}`
            },
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            // Update the profile image in content data
            contentData.personalInfo = contentData.personalInfo || {};
            contentData.personalInfo.profileImage = result.path;

            // Save the updated content
            await saveContent(contentData);

            // Update the preview image
            const currentProfileImage = document.getElementById('current-profile-image');
            if (currentProfileImage) {
                currentProfileImage.src = result.path;
            }

            showNotification('تم تحديث الصورة الشخصية بنجاح', 'success');
            input.value = ''; // Clear the input
        } else {
            showNotification('فشل في رفع الصورة الشخصية', 'error');
        }
    } catch (error) {
        console.error('Profile image upload error:', error);
        showNotification('حدث خطأ في رفع الصورة الشخصية', 'error');
    }
}

// ===== Utility Functions =====
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
