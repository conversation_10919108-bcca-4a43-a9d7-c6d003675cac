<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة الإدارة - يوسف محمد أسود جاسم</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700;800&family=Open+Sans:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            --primary-color: #004AAD;
            --secondary-color: #FFD700;
            --accent-color: #FFFFFF;
            --dark-color: #1E1E1E;
            --light-gray: #F8F9FA;
            --gradient-primary: linear-gradient(135deg, #004AAD 0%, #0066FF 100%);
            --shadow-light: 0 4px 20px rgba(0, 74, 173, 0.1);
            --shadow-medium: 0 8px 30px rgba(0, 74, 173, 0.15);
            --border-radius: 15px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            background: var(--light-gray);
            color: var(--dark-color);
            line-height: 1.6;
        }

        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .admin-header {
            background: var(--gradient-primary);
            color: var(--accent-color);
            padding: 2rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            text-align: center;
        }

        .admin-header h1 {
            font-family: 'Poppins', sans-serif;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .login-form {
            background: var(--accent-color);
            padding: 3rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-medium);
            max-width: 400px;
            margin: 0 auto;
        }

        .admin-panel {
            display: none;
        }

        .admin-nav {
            background: var(--accent-color);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            box-shadow: var(--shadow-light);
        }

        .admin-nav ul {
            list-style: none;
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .admin-nav button {
            background: var(--gradient-primary);
            color: var(--accent-color);
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            font-weight: 600;
            transition: var(--transition);
        }

        .admin-nav button:hover,
        .admin-nav button.active {
            background: var(--secondary-color);
            color: var(--dark-color);
            transform: translateY(-2px);
        }

        .admin-section {
            background: var(--accent-color);
            padding: 2rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-light);
            margin-bottom: 2rem;
            display: none;
        }

        .admin-section.active {
            display: block;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 1rem;
            border: 2px solid var(--light-gray);
            border-radius: var(--border-radius);
            font-size: 1rem;
            transition: var(--transition);
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 74, 173, 0.1);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: var(--accent-color);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: var(--dark-color);
        }

        .btn-danger {
            background: #dc3545;
            color: var(--accent-color);
        }

        .btn-success {
            background: #28a745;
            color: var(--accent-color);
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .card {
            background: var(--light-gray);
            padding: 1.5rem;
            border-radius: var(--border-radius);
            border: 2px solid transparent;
            transition: var(--transition);
        }

        .card:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        .card h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .file-upload {
            position: relative;
            display: inline-block;
            cursor: pointer;
            background: var(--light-gray);
            padding: 2rem;
            border: 2px dashed var(--primary-color);
            border-radius: var(--border-radius);
            text-align: center;
            width: 100%;
            transition: var(--transition);
        }

        .file-upload:hover {
            background: rgba(0, 74, 173, 0.1);
        }

        .file-upload input[type="file"] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 2rem;
            border-radius: var(--border-radius);
            color: var(--accent-color);
            font-weight: 600;
            z-index: 10000;
            transform: translateX(100%);
            transition: var(--transition);
        }

        .notification.success {
            background: #28a745;
        }

        .notification.error {
            background: #dc3545;
        }

        .notification.show {
            transform: translateX(0);
        }

        .logout-btn {
            position: absolute;
            top: 2rem;
            left: 2rem;
            background: rgba(255, 255, 255, 0.2);
            color: var(--accent-color);
            border: 2px solid var(--accent-color);
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
        }

        .logout-btn:hover {
            background: var(--accent-color);
            color: var(--primary-color);
        }

        @media (max-width: 768px) {
            .admin-container {
                padding: 1rem;
            }
            
            .admin-nav ul {
                flex-direction: column;
            }
            
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Login Form -->
        <div id="login-section">
            <div class="admin-header">
                <h1>لوحة الإدارة</h1>
                <p>إدارة محتوى الموقع الشخصي</p>
            </div>
            
            <form class="login-form" id="login-form">
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
            </form>
        </div>

        <!-- Admin Panel -->
        <div id="admin-panel" class="admin-panel">
            <div class="admin-header">
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
                <h1>لوحة الإدارة</h1>
                <p>مرحباً بك في لوحة إدارة الموقع</p>
            </div>

            <!-- Navigation -->
            <nav class="admin-nav">
                <ul>
                    <li><button onclick="showSection('personal', this)" class="active">المعلومات الشخصية</button></li>
                    <li><button onclick="showSection('about', this)">من أنا</button></li>
                    <li><button onclick="showSection('projects', this)">المشاريع</button></li>
                    <li><button onclick="showSection('services', this)">الخدمات</button></li>
                    <li><button onclick="showSection('products', this)">المنتجات</button></li>
                    <li><button onclick="showSection('testimonials', this)">التوصيات</button></li>
                    <li><button onclick="showSection('contact', this)">معلومات الاتصال</button></li>
                    <li><button onclick="showSection('images', this)">إدارة الصور</button></li>
                </ul>
            </nav>

            <!-- Personal Info Section -->
            <div id="personal-section" class="admin-section active">
                <h2>المعلومات الشخصية</h2>
                <form id="personal-form">
                    <div class="grid">
                        <div class="form-group">
                            <label for="fullName">الاسم الكامل</label>
                            <input type="text" id="fullName" name="fullName">
                        </div>
                        <div class="form-group">
                            <label for="displayName">الاسم المعروض</label>
                            <input type="text" id="displayName" name="displayName">
                        </div>
                        <div class="form-group">
                            <label for="title">المسمى الوظيفي</label>
                            <input type="text" id="title" name="title">
                        </div>
                        <div class="form-group">
                            <label for="birthDate">تاريخ الميلاد</label>
                            <input type="text" id="birthDate" name="birthDate">
                        </div>
                        <div class="form-group">
                            <label for="nationality">الجنسية</label>
                            <input type="text" id="nationality" name="nationality">
                        </div>
                        <div class="form-group">
                            <label for="education">التعليم</label>
                            <input type="text" id="education" name="education">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="description">الوصف</label>
                        <textarea id="description" name="description"></textarea>
                    </div>
                    <div class="form-group">
                        <label>الصورة الشخصية الحالية</label>
                        <div style="margin-bottom: 1rem;">
                            <img id="current-profile-image" src="/images/profile.jpg" alt="الصورة الشخصية" style="width: 150px; height: 150px; object-fit: cover; border-radius: 50%; border: 3px solid var(--primary-color);">
                        </div>
                        <label>تغيير الصورة الشخصية</label>
                        <div class="file-upload">
                            <input type="file" id="profile-image-upload" accept="image/*" onchange="updateProfileImage(this)">
                            <i class="fas fa-camera" style="font-size: 2rem; color: var(--primary-color);"></i>
                            <p>اضغط هنا لتغيير الصورة الشخصية</p>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ التغييرات
                    </button>
                </form>
            </div>

            <!-- About Section -->
            <div id="about-section" class="admin-section">
                <h2>قسم من أنا</h2>
                <form id="about-form">
                    <div class="form-group">
                        <label for="bio">السيرة الذاتية</label>
                        <textarea id="bio" name="bio"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="skills">المهارات (منفصلة بفاصلة)</label>
                        <textarea id="skills" name="skills" placeholder="HTML, CSS, JavaScript"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="interests">الاهتمامات (منفصلة بفاصلة)</label>
                        <textarea id="interests" name="interests" placeholder="التصميم, البرمجة, الإبداع"></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ التغييرات
                    </button>
                </form>
            </div>

            <!-- Projects Section -->
            <div id="projects-section" class="admin-section">
                <h2>إدارة المشاريع</h2>
                <button onclick="addProject()" class="btn btn-success">
                    <i class="fas fa-plus"></i>
                    إضافة مشروع جديد
                </button>
                <div id="projects-list" class="grid" style="margin-top: 2rem;">
                    <!-- Projects will be loaded here -->
                </div>
            </div>

            <!-- Services Section -->
            <div id="services-section" class="admin-section">
                <h2>إدارة الخدمات</h2>
                <button onclick="addService()" class="btn btn-success">
                    <i class="fas fa-plus"></i>
                    إضافة خدمة جديدة
                </button>
                <div id="services-list" class="grid" style="margin-top: 2rem;">
                    <!-- Services will be loaded here -->
                </div>
            </div>

            <!-- Products Section -->
            <div id="products-section" class="admin-section">
                <h2>إدارة المنتجات</h2>
                <button onclick="addProduct()" class="btn btn-success">
                    <i class="fas fa-plus"></i>
                    إضافة منتج جديد
                </button>
                <div id="products-list" class="grid" style="margin-top: 2rem;">
                    <!-- Products will be loaded here -->
                </div>
            </div>

            <!-- Testimonials Section -->
            <div id="testimonials-section" class="admin-section">
                <h2>إدارة التوصيات</h2>
                <button onclick="addTestimonial()" class="btn btn-success">
                    <i class="fas fa-plus"></i>
                    إضافة توصية جديدة
                </button>
                <div id="testimonials-list" class="grid" style="margin-top: 2rem;">
                    <!-- Testimonials will be loaded here -->
                </div>
            </div>

            <!-- Contact Section -->
            <div id="contact-section" class="admin-section">
                <h2>معلومات الاتصال</h2>
                <form id="contact-form">
                    <div class="grid">
                        <div class="form-group">
                            <label for="email">البريد الإلكتروني</label>
                            <input type="email" id="email" name="email">
                        </div>
                        <div class="form-group">
                            <label for="phone">الهاتف</label>
                            <input type="text" id="phone" name="phone">
                        </div>
                        <div class="form-group">
                            <label for="address">العنوان</label>
                            <input type="text" id="address" name="address">
                        </div>
                        <div class="form-group">
                            <label for="facebook">رابط فيسبوك</label>
                            <input type="url" id="facebook" name="facebook">
                        </div>
                        <div class="form-group">
                            <label for="instagram">رابط إنستغرام</label>
                            <input type="url" id="instagram" name="instagram">
                        </div>
                        <div class="form-group">
                            <label for="linkedin">رابط لينكد إن</label>
                            <input type="url" id="linkedin" name="linkedin">
                        </div>
                        <div class="form-group">
                            <label for="github">رابط جيت هاب</label>
                            <input type="url" id="github" name="github">
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ التغييرات
                    </button>
                </form>
            </div>

            <!-- Images Section -->
            <div id="images-section" class="admin-section">
                <h2>إدارة الصور</h2>
                <div class="form-group">
                    <label>رفع صورة جديدة</label>
                    <div class="file-upload">
                        <input type="file" id="image-upload" accept="image/*" onchange="uploadImage(this)">
                        <i class="fas fa-cloud-upload-alt" style="font-size: 2rem; color: var(--primary-color);"></i>
                        <p>اضغط هنا لرفع صورة أو اسحب الصورة إلى هنا</p>
                    </div>
                </div>
                <div id="uploaded-images" class="grid" style="margin-top: 2rem;">
                    <!-- Uploaded images will be shown here -->
                </div>
            </div>
        </div>
    </div>

    <script src="/js/admin.js"></script>
</body>
</html>
