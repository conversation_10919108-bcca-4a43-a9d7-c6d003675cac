const express = require('express');
const path = require('path');
const fs = require('fs');
const bcrypt = require('bcrypt');
const multer = require('multer');
const cors = require('cors');
const helmet = require('helmet');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, '../public')));

// Multer configuration for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, '../public/images/'));
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ storage: storage });

// Helper function to read content
function readContent() {
  try {
    const data = fs.readFileSync(path.join(__dirname, 'data/content.json'), 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading content:', error);
    return {};
  }
}

// Helper function to write content
function writeContent(content) {
  try {
    fs.writeFileSync(
      path.join(__dirname, 'data/content.json'),
      JSON.stringify(content, null, 2),
      'utf8'
    );
    return true;
  } catch (error) {
    console.error('Error writing content:', error);
    return false;
  }
}

// Admin credentials (hashed password for 'ym1792002')
const ADMIN_USERNAME = 'elzaeem';
const ADMIN_PASSWORD_HASH = '$2b$10$stE1UyqE10hnYijIbfxXr.1oTtSedJVmNV1G7dQG81K/kXK3uNW1q'; // ym1792002

// Session storage (in production, use proper session management)
const sessions = new Map();

// API Routes

// Get all content
app.get('/api/content', (req, res) => {
  const content = readContent();
  res.json(content);
});

// Admin login
app.post('/api/admin/login', async (req, res) => {
  const { username, password } = req.body;
  
  if (username === ADMIN_USERNAME && await bcrypt.compare(password, ADMIN_PASSWORD_HASH)) {
    const sessionId = Math.random().toString(36).substring(7);
    sessions.set(sessionId, { username, loginTime: Date.now() });
    res.json({ success: true, sessionId });
  } else {
    res.status(401).json({ success: false, message: 'بيانات الدخول غير صحيحة' });
  }
});

// Middleware to check admin authentication
function requireAuth(req, res, next) {
  const sessionId = req.headers.authorization?.replace('Bearer ', '');
  if (sessionId && sessions.has(sessionId)) {
    next();
  } else {
    res.status(401).json({ success: false, message: 'غير مصرح' });
  }
}

// Update content (admin only)
app.put('/api/admin/content', requireAuth, (req, res) => {
  const success = writeContent(req.body);
  if (success) {
    res.json({ success: true, message: 'تم تحديث المحتوى بنجاح' });
  } else {
    res.status(500).json({ success: false, message: 'خطأ في تحديث المحتوى' });
  }
});

// Upload image (admin only)
app.post('/api/admin/upload', requireAuth, upload.single('image'), (req, res) => {
  if (req.file) {
    res.json({ 
      success: true, 
      filename: req.file.filename,
      path: `/images/${req.file.filename}`
    });
  } else {
    res.status(400).json({ success: false, message: 'فشل في رفع الصورة' });
  }
});

// Contact form submission
app.post('/api/contact', (req, res) => {
  const { name, email, message } = req.body;
  
  // Here you would typically send an email or save to database
  console.log('Contact form submission:', { name, email, message });
  
  res.json({ success: true, message: 'تم إرسال رسالتك بنجاح' });
});

// Serve admin panel
app.get('/admin', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/admin.html'));
});

// Serve main site
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../public/index.html'));
});

app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
